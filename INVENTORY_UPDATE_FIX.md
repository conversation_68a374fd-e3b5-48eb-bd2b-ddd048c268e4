# Fix: Inventory bị xóa sau khi update sản phẩm

## Vấn đề

Khi update sản phẩm PHYSICAL và thêm inventory mới:
1. API trả về inventory với ID (ví dụ: "58") 
2. Nhưng kiểm tra database thì không có
3. API get detail cũng không thấy inventory

## Nguyên nhân

Vấn đề nằm ở **thứ tự thực hiện** trong `UpdateProductOrchestrator`:

### Thứ tự cũ (BỊ LỖI):
```
BƯỚC 6: processProductTypeSpecificUpdate → Tạo inventory mới (ID: 58)
BƯỚC 7: processClassificationsUpdate
BƯỚC 8: processAutoDeleteClassifications  
BƯỚC 9: processAutoDeleteInventory → XÓA inventory vừa tạo (ID: 58)
BƯỚC 10: buildUpdateResponse → Trả về inventory đã bị xóa
```

### Logic lỗi trong `processAutoDeleteInventory`:

```typescript
// Lấy IDs từ request
const requestIds = (updateDto.inventory || [])
  .map(inv => inv.inventoryId)  // ← inventoryId = undefined cho inventory mới
  .filter(id => id !== undefined) as number[];

// requestIds = [] (rỗng vì inventory mới chưa có ID)
// existingIds = [58] (inventory vừa tạo)
// idsToDelete = [58] (inventory vừa tạo bị đánh dấu để xóa)
```

## Giải pháp

Thay đổi thứ tự thực hiện để **xóa inventory cũ TRƯỚC KHI tạo mới**:

### Thứ tự mới (ĐÚNG):
```
BƯỚC 6: processAutoDeleteInventory → Xóa inventory cũ trước
BƯỚC 7: processProductTypeSpecificUpdate → Tạo inventory mới
BƯỚC 8: processClassificationsUpdate
BƯỚC 9: processAutoDeleteClassifications
BƯỚC 10: buildUpdateResponse → Trả về inventory mới
```

## Code thay đổi

**File**: `src/modules/business/user/services/processors/update/update-product-orchestrator.ts`

```typescript
// BƯỚC 6: Tự động phát hiện và xóa inventory CŨ TRƯỚC KHI tạo mới
await this.updateProcessor.processAutoDeleteInventory(
  id,
  updateProductDto,
  userId,
);

// BƯỚC 7: Xử lý cập nhật theo loại sản phẩm cụ thể (tạo inventory mới)
const productSpecificResult = await this.processProductTypeSpecificUpdate(
  product,
  updateProductDto,
  userId,
);
```

## Kết quả

Sau khi sửa:
1. ✅ Inventory cũ được xóa trước
2. ✅ Inventory mới được tạo và lưu vào database
3. ✅ API get detail sẽ trả về inventory mới
4. ✅ Không còn bị xóa nhầm inventory vừa tạo

## Test case

### Trước khi sửa:
```json
// API update response
"inventory": [{"id": "58", ...}]

// Database: KHÔNG CÓ ID 58
// API get detail: KHÔNG CÓ inventory
```

### Sau khi sửa:
```json
// API update response  
"inventory": [{"id": "58", ...}]

// Database: CÓ ID 58
// API get detail: CÓ inventory với ID 58
```

## Lưu ý

Logic `processAutoDeleteInventory` vẫn hoạt động đúng, chỉ cần thay đổi thứ tự thực hiện để tránh xóa nhầm inventory vừa tạo.
