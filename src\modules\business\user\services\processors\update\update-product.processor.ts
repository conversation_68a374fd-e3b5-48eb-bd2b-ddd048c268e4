import { Injectable, Logger } from '@nestjs/common';
import {
  UserProductRepository,
  CustomFieldRepository,
  InventoryRepository,
} from '@modules/business/repositories';
import {
  BusinessUpdateProductDto,
  BusinessProductResponseDto as ProductResponseDto,
  ClassificationResponseDto,
  CreateClassificationDto,
  UpdateClassificationDto,
} from '../../../dto';
import { InventoryResponseDto } from '../../../dto/inventory';
import { ProductTypeEnum, PriceTypeEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { UserProductHelper } from '../../../helpers/user-product.helper';
import { MetadataHelper } from '../../../helpers/metadata.helper';
import { ValidationHelper } from '../../../helpers/validation.helper';
import { ClassificationService } from '../../classification.service';
import { plainToInstance } from 'class-transformer';

/**
 * Processor chuyên xử lý logic cập nhật sản phẩm chung
 * Xử lý các logic chung cho tất cả loại sản phẩm
 * Logic chuyên biệt theo loại sản phẩm được xử lý bởi các processors riêng
 */
@Injectable()
export class UpdateProductProcessor {
  private readonly logger = new Logger(UpdateProductProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly inventoryRepository: InventoryRepository,
    private readonly userProductHelper: UserProductHelper,
    private readonly metadataHelper: MetadataHelper,
    private readonly validationHelper: ValidationHelper,
    private readonly classificationService: ClassificationService,
  ) {}

  /**
   * Tìm và validate sản phẩm tồn tại
   */
  async findAndValidateProduct(
    id: number,
    userId: number,
  ): Promise<UserProduct> {
    const product = await this.userProductRepository.findByIdAndUserId(
      id,
      userId,
    );

    if (!product) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
        `Không tìm thấy sản phẩm với ID ${id}`,
      );
    }

    return product;
  }

  /**
   * Cập nhật thông tin cơ bản của sản phẩm
   */
  updateBasicFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    // Cập nhật name
    if (updateDto.name !== undefined) {
      product.name = updateDto.name;
    }

    // Cập nhật productType
    if (updateDto.productType !== undefined) {
      product.productType = updateDto.productType;
    }

    // Cập nhật description
    if (updateDto.description !== undefined) {
      product.description = updateDto.description;
    }

    // Cập nhật tags
    if (updateDto.tags !== undefined) {
      product.tags = updateDto.tags;
    }

    // Cập nhật shipmentConfig
    if (updateDto.shipmentConfig !== undefined) {
      product.shipmentConfig = updateDto.shipmentConfig;
    }
  }

  /**
   * Xử lý cập nhật giá sản phẩm theo loại
   */
  updateProductPricing(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const finalProductType =
      updateDto.productType !== undefined
        ? updateDto.productType
        : product.productType;

    if (finalProductType === ProductTypeEnum.EVENT) {
      // Xử lý đặc biệt cho EVENT products
      product.price = null;
      product.typePrice = product.typePrice || PriceTypeEnum.HAS_PRICE;
      this.logger.log(
        'EVENT product detected in update - setting price to null and keeping typePrice as HAS_PRICE',
      );
    } else {
      // Xử lý cho các loại sản phẩm khác
      this.updateNormalProductPricing(product, updateDto);
    }
  }

  /**
   * Cập nhật giá cho sản phẩm thông thường (không phải EVENT)
   */
  private updateNormalProductPricing(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    if (updateDto.price !== undefined && updateDto.typePrice !== undefined) {
      // Validate giá khi có cả price và typePrice
      this.validationHelper.validateProductPrice(
        updateDto.price,
        updateDto.typePrice,
      );
      product.price = updateDto.price;
      product.typePrice = updateDto.typePrice;
    } else if (updateDto.price !== undefined) {
      // Chỉ cập nhật price
      product.price = updateDto.price;
    } else if (updateDto.typePrice !== undefined) {
      // Chỉ cập nhật typePrice
      product.typePrice = updateDto.typePrice;
    }
  }

  /**
   * Xử lý cập nhật custom fields
   */
  async updateCustomFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    if (updateDto.customFields === undefined) {
      return;
    }

    let customFields: any[] = [];

    if (updateDto.customFields && updateDto.customFields.length > 0) {
      this.logger.log(
        `Xử lý ${updateDto.customFields.length} custom fields cho sản phẩm`,
      );

      // Lấy danh sách ID custom fields
      const customFieldIds = this.metadataHelper.extractCustomFieldIds(
        updateDto.customFields,
      );

      // Lấy thông tin chi tiết custom fields từ database
      customFields = await this.customFieldRepository.findByIds(customFieldIds);

      // Validate custom fields
      this.metadataHelper.validateCustomFieldInputs(
        updateDto.customFields as never[],
        customFields,
      );
    }

    // Xử lý metadata
    const additionalMetadata = this.preserveExistingMetadata(product);

    // Cập nhật metadata cho sản phẩm
    const metadata = this.metadataHelper.buildMetadata(
      updateDto.customFields as never[],
      customFields,
      additionalMetadata,
    ) as never;
    product.metadata = metadata;
  }

  /**
   * Bảo tồn metadata hiện có
   */
  private preserveExistingMetadata(
    product: UserProduct,
  ): Record<string, unknown> {
    const additionalMetadata: Record<string, unknown> = {};

    // Giữ lại variant metadata hiện tại nếu có
    if (product.metadata?.variants) {
      additionalMetadata.variants = product.metadata.variants;
    }

    // Giữ lại service metadata hiện tại nếu có
    const serviceFields = [
      'serviceTime',
      'serviceDuration',
      'serviceProvider',
      'serviceType',
      'serviceLocation',
    ];
    serviceFields.forEach((field) => {
      if (product.metadata?.[field] !== undefined) {
        additionalMetadata[field] = product.metadata[field];
      }
    });

    return additionalMetadata;
  }

  /**
   * Finalize product update
   */
  finalizeProductUpdate(product: UserProduct): void {
    // Đảm bảo các trường embedding vẫn là null
    product.nameEmbedding = null;
    product.descriptionEmbedding = null;
    product.tagsEmbedding = null;

    // Cập nhật thời gian
    product.updatedAt = Date.now();
  }

  /**
   * Lưu sản phẩm đã cập nhật
   */
  async saveUpdatedProduct(product: UserProduct): Promise<UserProduct> {
    return await this.userProductRepository.save(product);
  }

  /**
   * Xử lý cập nhật classifications
   */
  async processClassificationsUpdate(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<{
    classifications: ClassificationResponseDto[];
    classificationUploadUrls: unknown[];
  }> {
    const classifications: ClassificationResponseDto[] = [];
    const classificationUploadUrls: unknown[] = [];

    if (updateDto.classifications && updateDto.classifications.length > 0) {
      this.logger.log(
        `Cập nhật ${updateDto.classifications.length} classifications cho sản phẩm ${productId}`,
      );

      for (const classificationDto of updateDto.classifications) {
        try {
          // Kiểm tra operation để quyết định tạo mới hay cập nhật
          const shouldCreateNew = classificationDto.operation === 'ADD' || !classificationDto.id;

          if (shouldCreateNew) {
            // Tạo classification mới (khi operation = ADD hoặc không có ID)
            this.logger.log(
              `Tạo classification mới với operation: ${classificationDto.operation || 'undefined'}, id: ${classificationDto.id || 'undefined'}`
            );
            const createClassificationDto: CreateClassificationDto = {
              type: classificationDto.type || '',
              description: classificationDto.description || '',
              price: classificationDto.price as never,
              customFields: classificationDto.customFields,
              imagesMediaTypes: classificationDto.imagesMediaTypes,
              sku: classificationDto.sku || `SKU-${Date.now()}`,
              minQuantityPerPurchase: classificationDto.minQuantityPerPurchase,
              maxQuantityPerPurchase: classificationDto.maxQuantityPerPurchase,
            };

            // Thêm imageOperations nếu có (cast để access imageOperations)
            const classificationDtoWithOperations = classificationDto as UpdateClassificationDto & {
              imageOperations?: Array<{
                operation: 'ADD' | 'DELETE';
                position?: number;
                key?: string;
                mimeType?: string;
              }>;
            };
            if (classificationDtoWithOperations.imageOperations) {
              (createClassificationDto as any).imageOperations = classificationDtoWithOperations.imageOperations;
            }

            const newClassification = await this.classificationService.create(
              productId,
              createClassificationDto,
              userId,
            );
            classifications.push(newClassification);

            // Thu thập upload URLs nếu có
            if (newClassification.uploadUrls) {
              classificationUploadUrls.push(newClassification.uploadUrls);
            }
          } else {
            // Cập nhật classification hiện có (khi operation = UPDATE hoặc không có operation nhưng có ID)
            this.logger.log(
              `Cập nhật classification hiện có với ID: ${classificationDto.id}`
            );

            const updatedClassification =
              await this.classificationService.update(
                classificationDto.id!,
                classificationDto,
                userId,
              );
            classifications.push(updatedClassification);

            // Thu thập upload URLs nếu có
            if (updatedClassification.uploadUrls) {
              classificationUploadUrls.push(updatedClassification.uploadUrls);
            }
          }
        } catch (error) {
          const errorMessage = (error as Error).message;
          const errorStack = (error as Error).stack;
          this.logger.error(
            `Lỗi khi xử lý classification: ${errorMessage}`,
            errorStack,
          );
          throw error;
        }
      }
    }

    return { classifications, classificationUploadUrls };
  }

  /**
   * Tự động phát hiện và xóa classifications bị xóa
   */
  async processAutoDeleteClassifications(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<void> {
    try {
      // Lấy tất cả classifications hiện có trong database
      const existingClassifications = await this.classificationService.getByProductId(productId);
      const existingIds = existingClassifications.map(c => c.id);

      // Lấy IDs từ request (classifications được giữ lại hoặc cập nhật)
      const requestIds = (updateDto.classifications || [])
        .map(c => c.id)
        .filter(id => id !== undefined) as number[];

      // Thêm IDs từ classificationsToDelete (nếu có)
      const explicitDeleteIds = updateDto.classificationsToDelete || [];

      // Tìm IDs cần xóa: có trong DB nhưng không có trong request và không trong explicitDeleteIds
      const idsToDelete = existingIds.filter(id =>
        !requestIds.includes(id) && !explicitDeleteIds.includes(id)
      );

      // Kết hợp với explicit delete IDs
      const allIdsToDelete = [...new Set([...idsToDelete, ...explicitDeleteIds])];

      if (allIdsToDelete.length > 0) {
        this.logger.log(
          `Tự động phát hiện ${idsToDelete.length} classifications cần xóa: ${idsToDelete.join(', ')}`
        );
        this.logger.log(
          `Explicit delete: ${explicitDeleteIds.length} classifications: ${explicitDeleteIds.join(', ')}`
        );
        this.logger.log(
          `Tổng cộng xóa ${allIdsToDelete.length} classifications: ${allIdsToDelete.join(', ')}`
        );

        await this.processClassificationsDeletion(productId, allIdsToDelete, userId);
      } else {
        this.logger.log(`Không có classification nào cần xóa cho sản phẩm ${productId}`);
      }
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Lỗi khi tự động phát hiện classifications cần xóa: ${errorMessage}`,
        errorStack,
      );
      // Không throw error để không cản trở việc cập nhật sản phẩm
    }
  }



  /**
   * Xử lý TẤT CẢ inventory operations (ADD/UPDATE/DELETE) ở BƯỚC 9
   * Logic tập trung: tất cả inventory operations được xử lý tại đây
   * Sử dụng @Transactional để đảm bảo tính nhất quán
   */
  @Transactional()
  async processAllInventoryOperations(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<InventoryResponseDto[]> {
    try {
      // Chỉ xử lý cho sản phẩm PHYSICAL
      const product = await this.userProductRepository.findById(productId);
      if (!product || product.productType !== 'PHYSICAL') {
        this.logger.log(`Sản phẩm ${productId} không phải PHYSICAL, bỏ qua xử lý inventory`);
        return [];
      }

      if (!updateDto.inventory || updateDto.inventory.length === 0) {
        this.logger.log(`Không có inventory data cho sản phẩm ${productId}`);
        return [];
      }

      this.logger.log(`🔄 BẮT ĐẦU xử lý TẤT CẢ ${updateDto.inventory.length} inventory operations cho sản phẩm ${productId}`);

      // BƯỚC 1: Xử lý DELETE operations trước
      await this.processInventoryDeleteOperations(productId, updateDto, userId);

      // BƯỚC 2: Xử lý ADD/UPDATE operations
      const inventoryResults = await this.processInventoryAddUpdateOperations(productId, updateDto, userId);

      this.logger.log(`✅ HOÀN THÀNH xử lý TẤT CẢ inventory operations cho sản phẩm ${productId}, tạo/cập nhật ${inventoryResults.length} inventory`);

      // Debug: Kiểm tra lại tất cả inventory của sản phẩm trong database
      const finalCheck = await this.inventoryRepository.findAll({ productId });
      this.logger.log(`🔍 KIỂM TRA CUỐI: Sản phẩm ${productId} có ${finalCheck.items?.length || 0} inventory trong database`);
      if (finalCheck.items && finalCheck.items.length > 0) {
        const inventoryIds = finalCheck.items.map(inv => inv.id);
        this.logger.log(`🔍 Danh sách inventory IDs: ${inventoryIds.join(', ')}`);
      }

      return inventoryResults;
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `❌ Lỗi khi xử lý TẤT CẢ inventory operations: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  /**
   * Xử lý DELETE inventory operations - CHỈ XÓA KHI CÓ operation: "DELETE"
   * Logic rõ ràng: không có logic tự động đoán, chỉ xóa khi frontend yêu cầu
   */
  async processInventoryDeleteOperations(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<void> {
    try {
      // Chỉ xử lý cho sản phẩm PHYSICAL
      const product = await this.userProductRepository.findById(productId);
      if (!product || product.productType !== 'PHYSICAL') {
        this.logger.log(`Sản phẩm ${productId} không phải PHYSICAL, bỏ qua xử lý inventory DELETE`);
        return;
      }

      if (!updateDto.inventory || updateDto.inventory.length === 0) {
        this.logger.log(`Không có inventory data cho sản phẩm ${productId}`);
        return;
      }

      // CHỈ xử lý các thao tác có operation: "DELETE"
      const deleteOperations = updateDto.inventory.filter(inv => inv.operation === 'DELETE');

      if (deleteOperations.length === 0) {
        this.logger.log(`Không có DELETE operations cho inventory của sản phẩm ${productId} - bỏ qua xóa`);
        return;
      }

      // Lấy danh sách ID cần xóa
      const idsToDelete = deleteOperations
        .map(inv => inv.inventoryId)
        .filter(id => id !== undefined) as number[];

      if (idsToDelete.length === 0) {
        this.logger.log(`DELETE operations không có inventoryId hợp lệ cho sản phẩm ${productId}`);
        return;
      }

      this.logger.log(
        `🗑️ Xử lý ${idsToDelete.length} DELETE operations cho inventory: ${idsToDelete.join(', ')}`
      );

      await this.processInventoryDeletion(productId, idsToDelete, userId);

      this.logger.log(`✅ Hoàn thành xử lý inventory DELETE operations cho sản phẩm ${productId}`);
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `❌ Lỗi khi xử lý inventory DELETE operations: ${errorMessage}`,
        errorStack,
      );
      // Không throw error để không cản trở việc cập nhật sản phẩm
    }
  }



  /**
   * Xử lý ADD/UPDATE inventory operations
   * Di chuyển logic từ PhysicalProductUpdateProcessor vào đây
   */
  async processInventoryAddUpdateOperations(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<InventoryResponseDto[]> {
    try {
      // Lọc chỉ lấy ADD/UPDATE operations
      const addUpdateOperations = (updateDto.inventory || []).filter(
        inv => inv.operation !== 'DELETE'
      );

      if (addUpdateOperations.length === 0) {
        this.logger.log(`Không có ADD/UPDATE operations cho inventory của sản phẩm ${productId}`);
        return [];
      }

      this.logger.log(`➕✏️ Xử lý ${addUpdateOperations.length} ADD/UPDATE operations cho inventory`);

      const inventoryResults: InventoryResponseDto[] = [];

      // Xử lý từng inventory operation
      for (const inventoryData of addUpdateOperations) {
        const result = await this.updateSingleInventory(productId, inventoryData, userId);
        if (result) {
          inventoryResults.push(result);
        }
      }

      return inventoryResults;
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `❌ Lỗi khi xử lý ADD/UPDATE inventory operations: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật một inventory item (di chuyển từ PhysicalProductUpdateProcessor)
   */
  private async updateSingleInventory(
    productId: number,
    inventoryData: any,
    userId: number,
  ): Promise<InventoryResponseDto | null> {
    try {
      // Cast để access operation field
      const inventoryWithOperation = inventoryData as any & {
        operation?: 'ADD' | 'UPDATE' | 'DELETE';
      };

      // Logic rõ ràng dựa trên operation field
      const operation = inventoryWithOperation.operation;

      // Bỏ qua DELETE operations (đã được xử lý ở bước trước)
      if (operation === 'DELETE') {
        this.logger.log(`Bỏ qua DELETE operation cho inventoryId: ${inventoryData.inventoryId} (đã được xử lý)`);
        return null;
      }

      // Quyết định tạo mới hay cập nhật
      const shouldCreateNew = operation === 'ADD' || !inventoryData.inventoryId;

      let inventory: any;

      if (shouldCreateNew) {
        // Tạo inventory mới
        this.logger.log(`➕ TẠO INVENTORY MỚI với operation: ${operation || 'undefined'}, warehouseId: ${inventoryData.warehouseId}, quantity: ${inventoryData.availableQuantity}`);

        const newInventory = {
          productId: productId,
          warehouseId: inventoryData.warehouseId || null,
          availableQuantity: inventoryData.availableQuantity || 0,
          currentQuantity: inventoryData.availableQuantity || 0,
          totalQuantity: inventoryData.availableQuantity || 0,
          reservedQuantity: 0,
          defectiveQuantity: 0,
          sku: inventoryData.sku || null,
          barcode: inventoryData.barcode || null,
          lastUpdated: Date.now(),
        };

        inventory = await this.inventoryRepository.save(newInventory);
        this.logger.log(`✅ ĐÃ LƯU INVENTORY MỚI VÀO DATABASE: ID=${inventory.id}, productId=${inventory.productId}`);

        // Kiểm tra lại xem inventory có thực sự tồn tại trong database không
        const verifyInventory = await this.inventoryRepository.findOne({ where: { id: inventory.id } });
        if (verifyInventory) {
          this.logger.log(`🔍 XÁC NHẬN: Inventory ID=${inventory.id} TỒN TẠI trong database`);
        } else {
          this.logger.error(`🚨 CẢNH BÁO: Inventory ID=${inventory.id} KHÔNG TỒN TẠI trong database sau khi save!`);
        }
      } else {
        // Cập nhật inventory hiện có
        this.logger.log(`✏️ CẬP NHẬT INVENTORY HIỆN CÓ với operation: ${operation || 'undefined'}, inventoryId: ${inventoryData.inventoryId}`);

        const existingInventory = await this.inventoryRepository.findOne({
          where: { id: inventoryData.inventoryId }
        });

        if (!existingInventory) {
          throw new Error(`Không tìm thấy inventory với ID ${inventoryData.inventoryId}`);
        }

        // Cập nhật inventory hiện có
        if (inventoryData.availableQuantity !== undefined) {
          existingInventory.availableQuantity = inventoryData.availableQuantity;
          existingInventory.currentQuantity = inventoryData.availableQuantity;
          existingInventory.totalQuantity = inventoryData.availableQuantity;
        }
        if (inventoryData.sku !== undefined) {
          existingInventory.sku = inventoryData.sku;
        }
        if (inventoryData.barcode !== undefined) {
          existingInventory.barcode = inventoryData.barcode;
        }
        if (inventoryData.warehouseId !== undefined) {
          existingInventory.warehouseId = inventoryData.warehouseId;
        }

        existingInventory.lastUpdated = Date.now();
        inventory = await this.inventoryRepository.save(existingInventory);
      }

      // Chuyển đổi sang DTO response
      const dto = new InventoryResponseDto();
      dto.id = inventory.id;
      dto.productId = inventory.productId;
      dto.warehouseId = inventory.warehouseId;
      dto.currentQuantity = inventory.currentQuantity;
      dto.totalQuantity = inventory.totalQuantity;
      dto.availableQuantity = inventory.availableQuantity;
      dto.reservedQuantity = inventory.reservedQuantity;
      dto.defectiveQuantity = inventory.defectiveQuantity;
      dto.lastUpdated = inventory.lastUpdated;
      dto.sku = inventory.sku;
      dto.barcode = inventory.barcode;

      // TODO: Lấy thông tin warehouse nếu cần
      // dto.warehouse = ...

      return dto;
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Error updating inventory for product ${productId}: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }

  /**
   * Tự động phát hiện và xóa inventory bị xóa (DEPRECATED - sử dụng processInventoryOperations thay thế)
   * Giữ lại để tương thích ngược
   */
  async processAutoDeleteInventory(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<void> {
    try {
      // Nếu có operation field trong inventory, CHỈ xử lý DELETE operations
      const hasOperations = updateDto.inventory?.some(inv => inv.operation !== undefined);
      if (hasOperations) {
        this.logger.log(`Phát hiện inventory operations - chỉ xử lý DELETE operations cho sản phẩm ${productId}`);
        return await this.processInventoryDeleteOperations(productId, updateDto, userId);
      }

      // Logic cũ cho tương thích ngược
      const product = await this.userProductRepository.findById(productId);
      if (!product || product.productType !== 'PHYSICAL') {
        this.logger.log(`Sản phẩm ${productId} không phải PHYSICAL, bỏ qua xử lý inventory`);
        return;
      }

      // Lấy tất cả inventory hiện có trong database
      const existingInventoriesResult = await this.inventoryRepository.findAll({ productId });
      const existingIds = existingInventoriesResult.items.map(inv => inv.id);

      // Lấy IDs từ request (inventory được giữ lại hoặc cập nhật)
      const requestIds = (updateDto.inventory || [])
        .map(inv => inv.inventoryId)
        .filter(id => id !== undefined) as number[];

      // Tìm IDs cần xóa: có trong DB nhưng không có trong request
      const idsToDelete = existingIds.filter(id => !requestIds.includes(id));

      if (idsToDelete.length > 0) {
        this.logger.log(
          `Tự động phát hiện ${idsToDelete.length} inventory cần xóa: ${idsToDelete.join(', ')}`
        );

        await this.processInventoryDeletion(productId, idsToDelete, userId);
      } else {
        this.logger.log(`Không có inventory nào cần xóa cho sản phẩm ${productId}`);
      }
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Lỗi khi tự động phát hiện inventory cần xóa: ${errorMessage}`,
        errorStack,
      );
      // Không throw error để không cản trở việc cập nhật sản phẩm
    }
  }

  /**
   * Xử lý xóa inventory
   */
  async processInventoryDeletion(
    productId: number,
    inventoryIds: number[],
    userId: number,
  ): Promise<void> {
    if (inventoryIds && inventoryIds.length > 0) {
      this.logger.log(
        `Xóa ${inventoryIds.length} inventory cho sản phẩm ${productId}`,
      );

      for (const inventoryId of inventoryIds) {
        try {
          await this.inventoryRepository.delete(inventoryId);
          this.logger.log(`❌ ĐÃ XÓA INVENTORY KHỎI DATABASE: ID=${inventoryId}`);
        } catch (error) {
          const errorMessage = (error as Error).message;
          const errorStack = (error as Error).stack;
          this.logger.error(
            `Lỗi khi xóa inventory ${inventoryId}: ${errorMessage}`,
            errorStack,
          );
          // Không throw error để tiếp tục xóa các inventory khác
        }
      }
    }
  }

  /**
   * Xử lý xóa classifications
   */
  async processClassificationsDeletion(
    productId: number,
    classificationIds: number[],
    userId: number,
  ): Promise<void> {
    if (classificationIds && classificationIds.length > 0) {
      this.logger.log(
        `Xóa ${classificationIds.length} classifications cho sản phẩm ${productId}`,
      );

      for (const classificationId of classificationIds) {
        try {
          await this.classificationService.delete(classificationId, userId);
        } catch (error) {
          const errorMessage = (error as Error).message;
          const errorStack = (error as Error).stack;
          this.logger.error(
            `Lỗi khi xóa classification ${classificationId}: ${errorMessage}`,
            errorStack,
          );
          // Không throw error để tiếp tục xóa các classification khác
        }
      }
    }
  }

  // Các method validate và process inventory đã được chuyển sang PhysicalProductUpdateProcessor

  /**
   * Xây dựng response cuối cùng
   */
  async buildUpdateResponse(
    product: UserProduct,
    imagesUploadUrls: unknown[],
    advancedImagesUploadUrls: unknown[],
    classificationUploadUrls: unknown[],
    classifications: ClassificationResponseDto[],
    inventory: unknown,
  ): Promise<ProductResponseDto> {
    // Chuyển đổi product entity sang DTO response
    const productDto =
      await this.userProductHelper.mapToProductResponseDto(product);

    // Thêm inventory nếu có trực tiếp vào productDto
    if (inventory) {
      (productDto as any).inventory = inventory;
    }

    // Thêm các thông tin bổ sung khác vào productDto
    if (imagesUploadUrls.length > 0) {
      (productDto as any).imagesUploadUrls = imagesUploadUrls;
    }

    if (advancedImagesUploadUrls.length > 0) {
      (productDto as any).advancedImagesUploadUrls = advancedImagesUploadUrls;
    }

    if (classificationUploadUrls.length > 0) {
      (productDto as any).classificationUploadUrls = classificationUploadUrls;
    }

    if (classifications.length > 0) {
      (productDto as any).classifications = classifications;
    }

    return productDto;
  }
}
