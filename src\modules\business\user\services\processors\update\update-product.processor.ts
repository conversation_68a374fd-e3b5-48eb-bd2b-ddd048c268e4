import { Injectable, Logger } from '@nestjs/common';
import {
  UserProductRepository,
  CustomFieldRepository,
  InventoryRepository,
} from '@modules/business/repositories';
import {
  BusinessUpdateProductDto,
  BusinessProductResponseDto as ProductResponseDto,
  ClassificationResponseDto,
  CreateClassificationDto,
  UpdateClassificationDto,
} from '../../../dto';
import { ProductTypeEnum, PriceTypeEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { UserProductHelper } from '../../../helpers/user-product.helper';
import { MetadataHelper } from '../../../helpers/metadata.helper';
import { ValidationHelper } from '../../../helpers/validation.helper';
import { ClassificationService } from '../../classification.service';
import { plainToInstance } from 'class-transformer';

/**
 * Processor chuyên xử lý logic cập nhật sản phẩm chung
 * Xử lý các logic chung cho tất cả loại sản phẩm
 * Logic chuyên biệt theo loại sản phẩm được xử lý bởi các processors riêng
 */
@Injectable()
export class UpdateProductProcessor {
  private readonly logger = new Logger(UpdateProductProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly inventoryRepository: InventoryRepository,
    private readonly userProductHelper: UserProductHelper,
    private readonly metadataHelper: MetadataHelper,
    private readonly validationHelper: ValidationHelper,
    private readonly classificationService: ClassificationService,
  ) {}

  /**
   * Tìm và validate sản phẩm tồn tại
   */
  async findAndValidateProduct(
    id: number,
    userId: number,
  ): Promise<UserProduct> {
    const product = await this.userProductRepository.findByIdAndUserId(
      id,
      userId,
    );

    if (!product) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
        `Không tìm thấy sản phẩm với ID ${id}`,
      );
    }

    return product;
  }

  /**
   * Cập nhật thông tin cơ bản của sản phẩm
   */
  updateBasicFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    // Cập nhật name
    if (updateDto.name !== undefined) {
      product.name = updateDto.name;
    }

    // Cập nhật productType
    if (updateDto.productType !== undefined) {
      product.productType = updateDto.productType;
    }

    // Cập nhật description
    if (updateDto.description !== undefined) {
      product.description = updateDto.description;
    }

    // Cập nhật tags
    if (updateDto.tags !== undefined) {
      product.tags = updateDto.tags;
    }

    // Cập nhật shipmentConfig
    if (updateDto.shipmentConfig !== undefined) {
      product.shipmentConfig = updateDto.shipmentConfig;
    }
  }

  /**
   * Xử lý cập nhật giá sản phẩm theo loại
   */
  updateProductPricing(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const finalProductType =
      updateDto.productType !== undefined
        ? updateDto.productType
        : product.productType;

    if (finalProductType === ProductTypeEnum.EVENT) {
      // Xử lý đặc biệt cho EVENT products
      product.price = null;
      product.typePrice = product.typePrice || PriceTypeEnum.HAS_PRICE;
      this.logger.log(
        'EVENT product detected in update - setting price to null and keeping typePrice as HAS_PRICE',
      );
    } else {
      // Xử lý cho các loại sản phẩm khác
      this.updateNormalProductPricing(product, updateDto);
    }
  }

  /**
   * Cập nhật giá cho sản phẩm thông thường (không phải EVENT)
   */
  private updateNormalProductPricing(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    if (updateDto.price !== undefined && updateDto.typePrice !== undefined) {
      // Validate giá khi có cả price và typePrice
      this.validationHelper.validateProductPrice(
        updateDto.price,
        updateDto.typePrice,
      );
      product.price = updateDto.price;
      product.typePrice = updateDto.typePrice;
    } else if (updateDto.price !== undefined) {
      // Chỉ cập nhật price
      product.price = updateDto.price;
    } else if (updateDto.typePrice !== undefined) {
      // Chỉ cập nhật typePrice
      product.typePrice = updateDto.typePrice;
    }
  }

  /**
   * Xử lý cập nhật custom fields
   */
  async updateCustomFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    if (updateDto.customFields === undefined) {
      return;
    }

    let customFields: any[] = [];

    if (updateDto.customFields && updateDto.customFields.length > 0) {
      this.logger.log(
        `Xử lý ${updateDto.customFields.length} custom fields cho sản phẩm`,
      );

      // Lấy danh sách ID custom fields
      const customFieldIds = this.metadataHelper.extractCustomFieldIds(
        updateDto.customFields,
      );

      // Lấy thông tin chi tiết custom fields từ database
      customFields = await this.customFieldRepository.findByIds(customFieldIds);

      // Validate custom fields
      this.metadataHelper.validateCustomFieldInputs(
        updateDto.customFields as never[],
        customFields,
      );
    }

    // Xử lý metadata
    const additionalMetadata = this.preserveExistingMetadata(product);

    // Cập nhật metadata cho sản phẩm
    const metadata = this.metadataHelper.buildMetadata(
      updateDto.customFields as never[],
      customFields,
      additionalMetadata,
    ) as never;
    product.metadata = metadata;
  }

  /**
   * Bảo tồn metadata hiện có
   */
  private preserveExistingMetadata(
    product: UserProduct,
  ): Record<string, unknown> {
    const additionalMetadata: Record<string, unknown> = {};

    // Giữ lại variant metadata hiện tại nếu có
    if (product.metadata?.variants) {
      additionalMetadata.variants = product.metadata.variants;
    }

    // Giữ lại service metadata hiện tại nếu có
    const serviceFields = [
      'serviceTime',
      'serviceDuration',
      'serviceProvider',
      'serviceType',
      'serviceLocation',
    ];
    serviceFields.forEach((field) => {
      if (product.metadata?.[field] !== undefined) {
        additionalMetadata[field] = product.metadata[field];
      }
    });

    return additionalMetadata;
  }

  /**
   * Finalize product update
   */
  finalizeProductUpdate(product: UserProduct): void {
    // Đảm bảo các trường embedding vẫn là null
    product.nameEmbedding = null;
    product.descriptionEmbedding = null;
    product.tagsEmbedding = null;

    // Cập nhật thời gian
    product.updatedAt = Date.now();
  }

  /**
   * Lưu sản phẩm đã cập nhật
   */
  async saveUpdatedProduct(product: UserProduct): Promise<UserProduct> {
    return await this.userProductRepository.save(product);
  }

  /**
   * Xử lý cập nhật classifications
   */
  async processClassificationsUpdate(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<{
    classifications: ClassificationResponseDto[];
    classificationUploadUrls: unknown[];
  }> {
    const classifications: ClassificationResponseDto[] = [];
    const classificationUploadUrls: unknown[] = [];

    if (updateDto.classifications && updateDto.classifications.length > 0) {
      this.logger.log(
        `Cập nhật ${updateDto.classifications.length} classifications cho sản phẩm ${productId}`,
      );

      for (const classificationDto of updateDto.classifications) {
        try {
          // Kiểm tra operation để quyết định tạo mới hay cập nhật
          const shouldCreateNew = classificationDto.operation === 'ADD' || !classificationDto.id;

          if (shouldCreateNew) {
            // Tạo classification mới (khi operation = ADD hoặc không có ID)
            this.logger.log(
              `Tạo classification mới với operation: ${classificationDto.operation || 'undefined'}, id: ${classificationDto.id || 'undefined'}`
            );
            const createClassificationDto: CreateClassificationDto = {
              type: classificationDto.type || '',
              description: classificationDto.description || '',
              price: classificationDto.price as never,
              customFields: classificationDto.customFields,
              imagesMediaTypes: classificationDto.imagesMediaTypes,
              sku: classificationDto.sku || `SKU-${Date.now()}`,
              minQuantityPerPurchase: classificationDto.minQuantityPerPurchase,
              maxQuantityPerPurchase: classificationDto.maxQuantityPerPurchase,
            };

            // Thêm imageOperations nếu có (cast để access imageOperations)
            const classificationDtoWithOperations = classificationDto as UpdateClassificationDto & {
              imageOperations?: Array<{
                operation: 'ADD' | 'DELETE';
                position?: number;
                key?: string;
                mimeType?: string;
              }>;
            };
            if (classificationDtoWithOperations.imageOperations) {
              (createClassificationDto as any).imageOperations = classificationDtoWithOperations.imageOperations;
            }

            const newClassification = await this.classificationService.create(
              productId,
              createClassificationDto,
              userId,
            );
            classifications.push(newClassification);

            // Thu thập upload URLs nếu có
            if (newClassification.uploadUrls) {
              classificationUploadUrls.push(newClassification.uploadUrls);
            }
          } else {
            // Cập nhật classification hiện có (khi operation = UPDATE hoặc không có operation nhưng có ID)
            this.logger.log(
              `Cập nhật classification hiện có với ID: ${classificationDto.id}`
            );

            const updatedClassification =
              await this.classificationService.update(
                classificationDto.id!,
                classificationDto,
                userId,
              );
            classifications.push(updatedClassification);

            // Thu thập upload URLs nếu có
            if (updatedClassification.uploadUrls) {
              classificationUploadUrls.push(updatedClassification.uploadUrls);
            }
          }
        } catch (error) {
          const errorMessage = (error as Error).message;
          const errorStack = (error as Error).stack;
          this.logger.error(
            `Lỗi khi xử lý classification: ${errorMessage}`,
            errorStack,
          );
          throw error;
        }
      }
    }

    return { classifications, classificationUploadUrls };
  }

  /**
   * Tự động phát hiện và xóa classifications bị xóa
   */
  async processAutoDeleteClassifications(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<void> {
    try {
      // Lấy tất cả classifications hiện có trong database
      const existingClassifications = await this.classificationService.getByProductId(productId);
      const existingIds = existingClassifications.map(c => c.id);

      // Lấy IDs từ request (classifications được giữ lại hoặc cập nhật)
      const requestIds = (updateDto.classifications || [])
        .map(c => c.id)
        .filter(id => id !== undefined) as number[];

      // Thêm IDs từ classificationsToDelete (nếu có)
      const explicitDeleteIds = updateDto.classificationsToDelete || [];

      // Tìm IDs cần xóa: có trong DB nhưng không có trong request và không trong explicitDeleteIds
      const idsToDelete = existingIds.filter(id =>
        !requestIds.includes(id) && !explicitDeleteIds.includes(id)
      );

      // Kết hợp với explicit delete IDs
      const allIdsToDelete = [...new Set([...idsToDelete, ...explicitDeleteIds])];

      if (allIdsToDelete.length > 0) {
        this.logger.log(
          `Tự động phát hiện ${idsToDelete.length} classifications cần xóa: ${idsToDelete.join(', ')}`
        );
        this.logger.log(
          `Explicit delete: ${explicitDeleteIds.length} classifications: ${explicitDeleteIds.join(', ')}`
        );
        this.logger.log(
          `Tổng cộng xóa ${allIdsToDelete.length} classifications: ${allIdsToDelete.join(', ')}`
        );

        await this.processClassificationsDeletion(productId, allIdsToDelete, userId);
      } else {
        this.logger.log(`Không có classification nào cần xóa cho sản phẩm ${productId}`);
      }
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Lỗi khi tự động phát hiện classifications cần xóa: ${errorMessage}`,
        errorStack,
      );
      // Không throw error để không cản trở việc cập nhật sản phẩm
    }
  }

  /**
   * Xử lý inventory operations (DELETE) dựa trên operation field từ frontend
   * Chỉ xử lý DELETE operations, ADD/UPDATE operations được xử lý bởi PhysicalProductUpdateProcessor
   */
  async processInventoryOperations(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<void> {
    try {
      // Chỉ xử lý cho sản phẩm PHYSICAL
      const product = await this.userProductRepository.findById(productId);
      if (!product || product.productType !== 'PHYSICAL') {
        this.logger.log(`Sản phẩm ${productId} không phải PHYSICAL, bỏ qua xử lý inventory`);
        return;
      }

      if (!updateDto.inventory || updateDto.inventory.length === 0) {
        this.logger.log(`Không có inventory operations cho sản phẩm ${productId}`);
        return;
      }

      // CHỈ xử lý các thao tác DELETE
      const deleteOperations = updateDto.inventory.filter(inv => inv.operation === 'DELETE');
      if (deleteOperations.length > 0) {
        const idsToDelete = deleteOperations
          .map(inv => inv.inventoryId)
          .filter(id => id !== undefined) as number[];

        if (idsToDelete.length > 0) {
          this.logger.log(
            `Xử lý ${idsToDelete.length} DELETE operations cho inventory: ${idsToDelete.join(', ')}`
          );
          await this.processInventoryDeletion(productId, idsToDelete, userId);
        }
      } else {
        this.logger.log(`Không có DELETE operations cho inventory của sản phẩm ${productId}`);
      }

      this.logger.log(`Hoàn thành xử lý inventory DELETE operations cho sản phẩm ${productId}`);
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Lỗi khi xử lý inventory operations: ${errorMessage}`,
        errorStack,
      );
      // Không throw error để không cản trở việc cập nhật sản phẩm
    }
  }

  /**
   * Tự động phát hiện và xóa inventory bị xóa (DEPRECATED - sử dụng processInventoryOperations thay thế)
   * Giữ lại để tương thích ngược
   */
  async processAutoDeleteInventory(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<void> {
    try {
      // Nếu có operation field trong inventory, CHỈ xử lý DELETE operations
      const hasOperations = updateDto.inventory?.some(inv => inv.operation !== undefined);
      if (hasOperations) {
        this.logger.log(`Phát hiện inventory operations - chỉ xử lý DELETE operations cho sản phẩm ${productId}`);
        return await this.processInventoryOperations(productId, updateDto, userId);
      }

      // Logic cũ cho tương thích ngược
      const product = await this.userProductRepository.findById(productId);
      if (!product || product.productType !== 'PHYSICAL') {
        this.logger.log(`Sản phẩm ${productId} không phải PHYSICAL, bỏ qua xử lý inventory`);
        return;
      }

      // Lấy tất cả inventory hiện có trong database
      const existingInventoriesResult = await this.inventoryRepository.findAll({ productId });
      const existingIds = existingInventoriesResult.items.map(inv => inv.id);

      // Lấy IDs từ request (inventory được giữ lại hoặc cập nhật)
      const requestIds = (updateDto.inventory || [])
        .map(inv => inv.inventoryId)
        .filter(id => id !== undefined) as number[];

      // Tìm IDs cần xóa: có trong DB nhưng không có trong request
      const idsToDelete = existingIds.filter(id => !requestIds.includes(id));

      if (idsToDelete.length > 0) {
        this.logger.log(
          `Tự động phát hiện ${idsToDelete.length} inventory cần xóa: ${idsToDelete.join(', ')}`
        );

        await this.processInventoryDeletion(productId, idsToDelete, userId);
      } else {
        this.logger.log(`Không có inventory nào cần xóa cho sản phẩm ${productId}`);
      }
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Lỗi khi tự động phát hiện inventory cần xóa: ${errorMessage}`,
        errorStack,
      );
      // Không throw error để không cản trở việc cập nhật sản phẩm
    }
  }

  /**
   * Xử lý xóa inventory
   */
  async processInventoryDeletion(
    productId: number,
    inventoryIds: number[],
    userId: number,
  ): Promise<void> {
    if (inventoryIds && inventoryIds.length > 0) {
      this.logger.log(
        `Xóa ${inventoryIds.length} inventory cho sản phẩm ${productId}`,
      );

      for (const inventoryId of inventoryIds) {
        try {
          await this.inventoryRepository.delete(inventoryId);
          this.logger.log(`❌ ĐÃ XÓA INVENTORY KHỎI DATABASE: ID=${inventoryId}`);
        } catch (error) {
          const errorMessage = (error as Error).message;
          const errorStack = (error as Error).stack;
          this.logger.error(
            `Lỗi khi xóa inventory ${inventoryId}: ${errorMessage}`,
            errorStack,
          );
          // Không throw error để tiếp tục xóa các inventory khác
        }
      }
    }
  }

  /**
   * Xử lý xóa classifications
   */
  async processClassificationsDeletion(
    productId: number,
    classificationIds: number[],
    userId: number,
  ): Promise<void> {
    if (classificationIds && classificationIds.length > 0) {
      this.logger.log(
        `Xóa ${classificationIds.length} classifications cho sản phẩm ${productId}`,
      );

      for (const classificationId of classificationIds) {
        try {
          await this.classificationService.delete(classificationId, userId);
        } catch (error) {
          const errorMessage = (error as Error).message;
          const errorStack = (error as Error).stack;
          this.logger.error(
            `Lỗi khi xóa classification ${classificationId}: ${errorMessage}`,
            errorStack,
          );
          // Không throw error để tiếp tục xóa các classification khác
        }
      }
    }
  }

  // Các method validate và process inventory đã được chuyển sang PhysicalProductUpdateProcessor

  /**
   * Xây dựng response cuối cùng
   */
  async buildUpdateResponse(
    product: UserProduct,
    imagesUploadUrls: unknown[],
    advancedImagesUploadUrls: unknown[],
    classificationUploadUrls: unknown[],
    classifications: ClassificationResponseDto[],
    inventory: unknown,
  ): Promise<ProductResponseDto> {
    // Chuyển đổi product entity sang DTO response
    const productDto =
      await this.userProductHelper.mapToProductResponseDto(product);

    // Thêm inventory nếu có trực tiếp vào productDto
    if (inventory) {
      (productDto as any).inventory = inventory;
    }

    // Thêm các thông tin bổ sung khác vào productDto
    if (imagesUploadUrls.length > 0) {
      (productDto as any).imagesUploadUrls = imagesUploadUrls;
    }

    if (advancedImagesUploadUrls.length > 0) {
      (productDto as any).advancedImagesUploadUrls = advancedImagesUploadUrls;
    }

    if (classificationUploadUrls.length > 0) {
      (productDto as any).classificationUploadUrls = classificationUploadUrls;
    }

    if (classifications.length > 0) {
      (productDto as any).classifications = classifications;
    }

    return productDto;
  }
}
