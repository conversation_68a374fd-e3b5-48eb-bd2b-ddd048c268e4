import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserProductRepository,
  InventoryRepository,
  PhysicalWarehouseRepository,
} from '@modules/business/repositories';
import { BusinessUpdateProductDto, ProductInventoryDto } from '../../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct, Inventory } from '@modules/business/entities';
import { ValidationHelper } from '../../../helpers/validation.helper';
import { InventoryResponseDto } from '../../../dto/inventory';
import { UpdateProductResult } from './update-product-orchestrator';

/**
 * Processor chuyên xử lý cập nhật sản phẩm vật lý (PHYSICAL)
 * Xử lý inventory, shipment config, warehouse management
 */
@Injectable()
export class PhysicalProductUpdateProcessor {
  private readonly logger = new Logger(PhysicalProductUpdateProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly inventoryRepository: InventoryRepository,
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Cập nhật sản phẩm vật lý hoàn chỉnh
   */
  @Transactional()
  async updatePhysicalProduct(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(
      `Updating PHYSICAL product: ${product.name} (ID: ${product.id})`,
    );

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm vật lý
    await this.validatePhysicalProductData(updateDto);

    // BƯỚC 2: Cập nhật shipment config
    this.updateShipmentConfig(product, updateDto);

    // BƯỚC 3: Bỏ qua inventory update (sẽ được xử lý ở BƯỚC 9 trong orchestrator)

    // BƯỚC 4: Lưu sản phẩm đã cập nhật
    const updatedProduct = await this.userProductRepository.save(product);

    return {
      product: updatedProduct,
      imagesUploadUrls: [],
      advancedImagesUploadUrls: [],
      classificationUploadUrls: [],
      classifications: [],
      inventory: [], // Inventory sẽ được xử lý ở BƯỚC 9
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm vật lý
   */
  private async validatePhysicalProductData(
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    // Validate shipment config nếu có
    if (updateDto.shipmentConfig) {
      this.validateShipmentConfig(updateDto.shipmentConfig);
    }

    // Validate inventory data nếu có
    if (updateDto.inventory) {
      await this.validateInventoryData(updateDto.inventory);
    }
  }

  /**
   * Validate shipment config
   */
  private validateShipmentConfig(shipmentConfig: unknown): void {
    const config = shipmentConfig as Record<string, unknown>;

    if (config.widthCm !== undefined && (config.widthCm as number) < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Chiều rộng không thể âm',
      );
    }

    if (config.heightCm !== undefined && (config.heightCm as number) < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Chiều cao không thể âm',
      );
    }

    if (config.lengthCm !== undefined && (config.lengthCm as number) < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Chiều dài không thể âm',
      );
    }

    if (config.weightGram !== undefined && (config.weightGram as number) < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Trọng lượng không thể âm',
      );
    }
  }

  /**
   * Validate inventory data
   */
  private async validateInventoryData(inventoryData: unknown): Promise<void> {
    if (Array.isArray(inventoryData)) {
      for (const item of inventoryData) {
        const inventoryItem = item as Record<string, unknown>;
        if (
          inventoryItem.availableQuantity !== undefined &&
          (inventoryItem.availableQuantity as number) < 0
        ) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            'Số lượng tồn kho không thể âm',
          );
        }

        // Validate warehouse nếu có
        if (inventoryItem.warehouseId) {
          await this.validateWarehouse(inventoryItem.warehouseId as number);
        }

        // Validate SKU unique nếu có
        if (inventoryItem.sku) {
          await this.validateSkuUnique(
            inventoryItem.sku as string,
            inventoryItem.productId as number,
          );
        }
      }
    } else {
      const inventoryItem = inventoryData as Record<string, unknown>;
      if (
        inventoryItem.availableQuantity !== undefined &&
        (inventoryItem.availableQuantity as number) < 0
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng tồn kho không thể âm',
        );
      }
    }
  }

  /**
   * Validate warehouse tồn tại
   */
  private async validateWarehouse(warehouseId: number): Promise<void> {
    const warehouse =
      await this.physicalWarehouseRepository.findByWarehouseId_user(
        warehouseId,
      );
    if (!warehouse) {
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
        `Kho với ID ${warehouseId} không tồn tại`,
      );
    }
  }

  /**
   * Validate SKU unique
   */
  private async validateSkuUnique(
    sku: string,
    productId: number,
  ): Promise<void> {
    const existingInventory = await this.inventoryRepository.findBySkuAndUserId(
      sku,
      productId,
    );
    if (existingInventory && existingInventory.productId !== productId) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Mã SKU "${sku}" đã tồn tại trong sản phẩm khác`,
      );
    }
  }

  /**
   * Cập nhật shipment config
   */
  private updateShipmentConfig(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    if (updateDto.shipmentConfig !== undefined) {
      product.shipmentConfig = updateDto.shipmentConfig;
      this.logger.log(`Updated shipment config for product ${product.id}`);
    }
  }

  /**
   * Xử lý cập nhật inventory
   */
  private async processInventoryUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<InventoryResponseDto[]> {
    if (!updateDto.inventory) {
      return [];
    }

    this.logger.log(
      `Processing inventory update for PHYSICAL product ${product.id}`,
    );

    const inventoryResults: InventoryResponseDto[] = [];

    // Xử lý inventory data
    if (Array.isArray(updateDto.inventory)) {
      // Multiple inventory items
      for (const inventoryItem of updateDto.inventory) {
        const result = await this.updateSingleInventory(
          product.id,
          inventoryItem,
          userId,
        );
        if (result) {
          inventoryResults.push(result);
        }
      }
    } else {
      // Single inventory item
      const result = await this.updateSingleInventory(
        product.id,
        updateDto.inventory,
        userId,
      );
      if (result) {
        inventoryResults.push(result);
      }
    }

    return inventoryResults;
  }

  /**
   * Cập nhật một inventory item
   */
  private async updateSingleInventory(
    productId: number,
    inventoryData: ProductInventoryDto,
    _userId: number,
  ): Promise<InventoryResponseDto | null> {
    try {
      // Cast để access operation field
      const inventoryWithOperation = inventoryData as ProductInventoryDto & {
        operation?: 'ADD' | 'UPDATE' | 'DELETE';
      };

      // Logic rõ ràng dựa trên operation field
      const operation = inventoryWithOperation.operation;

      // Bỏ qua DELETE operations (được xử lý ở bước khác)
      if (operation === 'DELETE') {
        this.logger.log(`Bỏ qua DELETE operation cho inventoryId: ${inventoryData.inventoryId} (được xử lý ở bước khác)`);
        return null;
      }

      // Quyết định tạo mới hay cập nhật
      const shouldCreateNew = operation === 'ADD' || !inventoryData.inventoryId;

      let inventory: Inventory;

      if (shouldCreateNew) {
        // Tạo inventory mới (khi operation = ADD hoặc không có inventoryId)
        this.logger.log(
          `➕ TẠO INVENTORY MỚI với operation: ${operation || 'undefined'}, warehouseId: ${inventoryData.warehouseId}, quantity: ${inventoryData.availableQuantity}`
        );

        const newInventory = new Inventory();
        newInventory.productId = productId;
        newInventory.warehouseId = inventoryData.warehouseId || null;
        newInventory.availableQuantity = inventoryData.availableQuantity || 0;
        newInventory.sku = inventoryData.sku || null;
        newInventory.barcode = inventoryData.barcode || null;

        // Tính toán số lượng
        this.validationHelper.calculateInventoryQuantities(newInventory);
        newInventory.lastUpdated = Date.now();

        inventory = await this.inventoryRepository.save(newInventory);
        this.logger.log(`✅ ĐÃ LƯU INVENTORY MỚI VÀO DATABASE: ID=${inventory.id}, productId=${inventory.productId}`);
      } else {
        // Cập nhật inventory hiện có (khi operation = UPDATE hoặc có inventoryId)
        this.logger.log(
          `✏️ CẬP NHẬT INVENTORY HIỆN CÓ với operation: ${operation || 'undefined'}, inventoryId: ${inventoryData.inventoryId}`
        );

        const existingInventory = await this.inventoryRepository.findOne({
          where: { id: inventoryData.inventoryId }
        });

        if (!existingInventory) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVENTORY_NOT_FOUND,
            `Không tìm thấy inventory với ID ${inventoryData.inventoryId}`,
          );
        }

        // Cập nhật inventory hiện có
        if (inventoryData.availableQuantity !== undefined) {
          existingInventory.availableQuantity = inventoryData.availableQuantity;
        }
        if (inventoryData.sku !== undefined) {
          existingInventory.sku = inventoryData.sku;
        }
        if (inventoryData.barcode !== undefined) {
          existingInventory.barcode = inventoryData.barcode;
        }
        if (inventoryData.warehouseId !== undefined) {
          existingInventory.warehouseId = inventoryData.warehouseId;
        }

        // Tính toán lại số lượng
        this.validationHelper.calculateInventoryQuantities(existingInventory);
        existingInventory.lastUpdated = Date.now();

        inventory = await this.inventoryRepository.save(existingInventory);
      }

      // Chuyển đổi sang DTO response
      const dto = new InventoryResponseDto();
      dto.id = inventory.id;
      // Use _userId for future extensibility
      void _userId;
      dto.productId = inventory.productId;
      dto.warehouseId = inventory.warehouseId;
      dto.currentQuantity = inventory.currentQuantity;
      dto.totalQuantity = inventory.totalQuantity;
      dto.availableQuantity = inventory.availableQuantity;
      dto.reservedQuantity = inventory.reservedQuantity;
      dto.defectiveQuantity = inventory.defectiveQuantity;
      dto.lastUpdated = inventory.lastUpdated;
      dto.sku = inventory.sku;
      dto.barcode = inventory.barcode;

      // Lấy thông tin warehouse nếu có
      if (inventory.warehouseId) {
        const warehouse =
          (await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(
            inventory.warehouseId,
          )) as Record<string, unknown> | null;
        if (warehouse) {
          dto.warehouse = {
            id: warehouse.id as number,
            warehouseId: warehouse.warehouseId as number,
            name: warehouse.name as string,
            description: warehouse.description as string,
            type: warehouse.type as never,
            address: warehouse.address as string,
            capacity: warehouse.capacity as number,
          };
        }
      }

      return dto;
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Error updating inventory for product ${productId}: ${errorMessage}`,
        errorStack,
      );
      throw error;
    }
  }
}
